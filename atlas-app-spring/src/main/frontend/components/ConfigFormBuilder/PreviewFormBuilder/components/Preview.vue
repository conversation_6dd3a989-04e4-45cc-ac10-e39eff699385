<template>
    <v-card class="preview">
        <div class="px-4 pt-4">
            <h2 class="preview-text">Preview</h2>
            <v-divider></v-divider>
            <div class="py-6">
                <slot> </slot>
            </div>
        </div>
    </v-card>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "Preview",
});
</script>

<style scoped lang="scss">
.preview {
    height: inherit;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: none !important;
}
.preview-text {
    font-size: px2rem(16);
    margin-bottom: 4px;
}
</style>
