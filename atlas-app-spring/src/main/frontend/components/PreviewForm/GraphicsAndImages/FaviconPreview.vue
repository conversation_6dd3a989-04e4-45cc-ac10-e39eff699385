<template>
    <div class="favicon-preview-wrapper">
        <!-- Browser Preview using background image -->
        <div class="browser-template">
            <!-- Favicon overlay positioned exactly where the blue circle is -->
            <div class="favicon-overlay">
                <img v-if="faviconUrl" :src="faviconUrl" alt="Favicon Preview" class="favicon-image" />
                <div v-else class="favicon-placeholder">
                    <!-- Blue circle placeholder to match the template -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "FaviconPreview",
    props: {
        faviconUrl: {
            type: String,
            default: null,
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.favicon-preview-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    width: 100%;
    margin-top: 35px;
}

.browser-template {
    width: 340px;
    height: 84px;
    background-image: url("/favicon-browser-template.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0 auto;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
        width: 470px;
    }
}

.favicon-overlay {
    position: absolute;
    top: 28px;
    left: 49px;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
        top: 24px;
        left: 70px;
        width: 16px;
        height: 16px;
    }
}

.favicon-image {
    width: 16px;
    height: 16px;
    object-fit: contain;
    border-radius: 2px;
}

.favicon-placeholder {
    width: 16px;
    height: 16px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
