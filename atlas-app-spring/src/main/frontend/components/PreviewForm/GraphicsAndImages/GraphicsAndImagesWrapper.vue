<template>
    <v-expansion-panels v-model="panels" class="d-flex flex-column wrapper-container" accordion multiple>
        <PreviewBuilder
            class="mb-2"
            title="Logo"
            description="This determines the logo utilized on the CarSaver's site."
        >
            <template #preview>
                <Preview :form="form" type="logo" class="preview" />
            </template>

            <template #configuration>
                <v-expansion-panels v-model="logoSubPanels" accordion multiple class="configuration">
                    <SubCollapsible title="Image">
                        <template #body>
                            <FileUpload
                                id="logo-image"
                                :initial-file="logoInitialFile"
                                :max-size="2 * 1024 * 1024"
                                upload-instructions="JPG, PNG file type 2 MB max file size"
                                recommendation="*We recommend a PNG with a transparent background and 40px max height."
                                @file-selected="handleLogoFileSelected"
                                @file-removed="handleLogoFileRemoved"
                            />
                        </template>
                    </SubCollapsible>
                </v-expansion-panels>
            </template>
        </PreviewBuilder>

        <PreviewBuilder class="mb-2" title="Favicon" description="This determines the favicon utilized in the browser.">
            <template #preview>
                <Preview :form="form" type="favicon" class="preview" />
            </template>

            <template #configuration>
                <v-expansion-panels v-model="faviconSubPanels" accordion multiple class="configuration">
                    <SubCollapsible title="Image">
                        <template #body>
                            <FileUpload
                                id="favicon-image"
                                :initial-file="faviconInitialFile"
                                :max-size="2 * 1024 * 1024"
                                upload-instructions="JPG, PNG file type 2 MB max file size"
                                recommendation="*We recommend 16x16px."
                                @file-selected="handleFaviconFileSelected"
                                @file-removed="handleFaviconFileRemoved"
                            />
                        </template>
                    </SubCollapsible>
                </v-expansion-panels>
            </template>
        </PreviewBuilder>

        <PreviewBuilder
            title="Dealership Images"
            description="This determines the dealership image (storefront or showroom) utilized in marketing materials."
        >
            <template #preview>
                <Preview :form="form" type="dealership" class="preview" />
            </template>

            <template #configuration>
                <v-expansion-panels v-model="dealershipSubPanels" accordion multiple class="configuration">
                    <SubCollapsible title="Images">
                        <template #body>
                            <FileUpload
                                id="dealership-image-1"
                                label="Mobile"
                                :initial-file="dealershipMobileInitialFile"
                                :max-size="5 * 1024 * 1024"
                                upload-instructions="JPG, PNG file type 5 MB max file size"
                                recommendation="*We recommend a 1:1 aspect ratio."
                                @file-selected="handleDealershipFile1Selected"
                                @file-removed="handleDealershipFile1Removed"
                            />

                            <FileUpload
                                id="dealership-image-2"
                                label="Desktop"
                                :initial-file="dealershipDesktopInitialFile"
                                :max-size="5 * 1024 * 1024"
                                upload-instructions="JPG, PNG file type 5 MB max file size"
                                recommendation="*We recommend a 16:9 aspect ratio."
                                @file-selected="handleDealershipFile2Selected"
                                @file-removed="handleDealershipFile2Removed"
                            />
                        </template>
                    </SubCollapsible>
                </v-expansion-panels>
            </template>
        </PreviewBuilder>
    </v-expansion-panels>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import PreviewBuilder from "Components/ConfigFormBuilder/PreviewFormBuilder/index.vue";
import Preview from "./Preview.vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible/index.vue";
import FileUpload from "Components/common/FileUpload.vue";

export default {
    name: "GraphicsAndImagesWrapper",
    components: {
        FileUpload,
        SubCollapsible,
        PreviewBuilder,
        Preview,
    },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            localPanels: [0, 1, 2],
            localSubPanels: [0, 1],
            logoSubPanels: [0],
            faviconSubPanels: [0],
            dealershipSubPanels: [0],
            valid: false,
            form: {
                logoFile: null,
                logoPreviewUrl: null,
                faviconFile: null,
                faviconPreviewUrl: null,
                dealershipFile1: null,
                dealershipFile1PreviewUrl: null,
                dealershipFile2: null,
                dealershipFile2PreviewUrl: null,
            },
        };
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        panels: sync("pageConfigs/panels"),
        subPanels: sync("pageConfigs/subPanels"),
        expandAll: sync("pageConfigs/expandAll"),
        formData: sync("pageConfigs/form@data"),
        formDefaults: get("pageConfigs/form@defaults"),
        flattenedPageBuilderData: sync("pageConfigs/flattenedPageBuilderData"),
        saveButtonLoading: get("pageConfigs/saveForm@loader"),
        userId: get("loggedInUser/userId"),
        pageName: get("pageConfigs/pageBuilderData@page"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        dealerId() {
            return this.selectedDealer?.id || this.$route.params.dealerId;
        },
        isInitLoadingComplete() {
            return this.getPageBuilderLoader.isComplete && this.getFormDefaultsLoader.isComplete;
        },
        logoInitialFile() {
            if (this.formDefaults?.logoUrl && this.formDefaults?.logoName) {
                return {
                    url: this.formDefaults.logoUrl,
                    name: this.formDefaults.logoName,
                    size: this.formDefaults.logoSize,
                };
            }
            return null;
        },
        faviconInitialFile() {
            if (this.formDefaults?.favIconUrl && this.formDefaults?.favIconName) {
                return {
                    url: this.formDefaults.favIconUrl,
                    name: this.formDefaults.favIconName,
                    size: this.formDefaults.favIconSize,
                };
            }
            return null;
        },
        dealershipMobileInitialFile() {
            if (this.formDefaults?.dealershipImages?.mobileUrl && this.formDefaults?.dealershipImages?.mobileName) {
                return {
                    url: this.formDefaults.dealershipImages.mobileUrl,
                    name: this.formDefaults.dealershipImages.mobileName,
                    size: this.formDefaults.dealershipImages.mobileSize,
                };
            }
            return null;
        },
        dealershipDesktopInitialFile() {
            if (this.formDefaults?.dealershipImages?.desktopUrl && this.formDefaults?.dealershipImages?.desktopName) {
                return {
                    url: this.formDefaults.dealershipImages.desktopUrl,
                    name: this.formDefaults.dealershipImages.desktopName,
                    size: this.formDefaults.dealershipImages.desktopSize,
                };
            }
            return null;
        },
    },
    watch: {
        isInitLoadingComplete: {
            handler(val) {
                if (val) {
                    this.initFormData();
                }
            },
            immediate: true,
        },
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllPanels();
                } else {
                    this.closeAllPanels();
                }
            },
            immediate: true,
        },
        formDefaults: {
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {
                    this.updatePreviewUrls(newValue);
                }
            },
            immediate: true,
            deep: true,
        },
    },
    mounted() {
        this.handleSetPanels(this.localPanels);
        this.handleSetSubPanels(this.localSubPanels);
        this.setIsMultipartForm({ isMultipart: true });
    },
    methods: {
        handleSetPanels: call("pageConfigs/handleSetPanels"),
        handleSetSubPanels: call("pageConfigs/handleSetSubPanels"),
        updateFormData: call("pageConfigs/updateFormData"),
        saveFormData: call("pageConfigs/saveFormData"),
        setIsMultipartForm: call("pageConfigs/setIsMultipartForm"),
        initFormData() {
            this.formData = { ...this.formDefaults };

            // Populate preview URLs from existing graphics config data
            if (this.formDefaults) {
                this.updatePreviewUrls(this.formDefaults);
            }
        },
        expandAllPanels() {
            this.expandPanels();
            this.expandAllSubPanels();
        },
        closeAllPanels() {
            this.panels = [];
            this.logoSubPanels = [];
            this.faviconSubPanels = [];
            this.dealershipSubPanels = [];
        },
        expandPanels() {
            this.panels = [...this.localPanels];
        },
        expandAllSubPanels() {
            this.logoSubPanels = [0];
            this.faviconSubPanels = [0];
            this.dealershipSubPanels = [0];
        },
        updatePreviewUrls(formDefaults) {
            // Set logo preview URL
            if (formDefaults.logoUrl && !this.form.logoFile) {
                this.form.logoPreviewUrl = formDefaults.logoUrl;
            }

            // Set favicon preview URL
            if (formDefaults.favIconUrl && !this.form.faviconFile) {
                this.form.faviconPreviewUrl = formDefaults.favIconUrl;
            }

            // Set dealership mobile image preview URL
            if (formDefaults.dealershipImages?.mobileUrl && !this.form.dealershipFile1) {
                this.form.dealershipFile1PreviewUrl = formDefaults.dealershipImages.mobileUrl;
            }

            // Set dealership desktop image preview URL
            if (formDefaults.dealershipImages?.desktopUrl && !this.form.dealershipFile2) {
                this.form.dealershipFile2PreviewUrl = formDefaults.dealershipImages.desktopUrl;
            }
        },
        // Logo file handlers
        handleLogoFileSelected(file) {
            this.form.logoFile = file;
            if (file && file.type.includes("image")) {
                this.form.logoPreviewUrl = URL.createObjectURL(file);
            }
            this.updateFormData({
                key: "logoFile",
                value: this.form.logoFile,
            });
        },
        handleLogoFileRemoved() {
            if (this.form.logoPreviewUrl) {
                URL.revokeObjectURL(this.form.logoPreviewUrl);
            }
            this.form.logoFile = null;
            this.form.logoPreviewUrl = null;
            this.updateFormData({
                key: "logoFile",
                value: null,
            });
        },
        // Favicon file handlers
        handleFaviconFileSelected(file) {
            this.form.faviconFile = file;
            if (file && file.type.includes("image")) {
                this.form.faviconPreviewUrl = URL.createObjectURL(file);
            }
            this.updateFormData({
                key: "faviconFile",
                value: this.form.faviconFile,
            });
        },
        handleFaviconFileRemoved() {
            if (this.form.faviconPreviewUrl) {
                URL.revokeObjectURL(this.form.faviconPreviewUrl);
            }
            this.form.faviconFile = null;
            this.form.faviconPreviewUrl = null;
            this.updateFormData({
                key: "faviconFile",
                value: null,
            });
        },
        // Dealership image handlers
        handleDealershipFile1Selected(file) {
            this.form.dealershipFile1 = file;
            if (file && file.type.includes("image")) {
                this.form.dealershipFile1PreviewUrl = URL.createObjectURL(file);
            }
            this.updateFormData({
                key: "dealershipFile1",
                value: this.form.dealershipFile1,
            });
        },
        handleDealershipFile1Removed() {
            if (this.form.dealershipFile1PreviewUrl) {
                URL.revokeObjectURL(this.form.dealershipFile1PreviewUrl);
            }
            this.form.dealershipFile1 = null;
            this.form.dealershipFile1PreviewUrl = null;
            this.updateFormData({
                key: "dealershipFile1",
                value: null,
            });
        },
        handleDealershipFile2Selected(file) {
            this.form.dealershipFile2 = file;
            if (file && file.type.includes("image")) {
                this.form.dealershipFile2PreviewUrl = URL.createObjectURL(file);
            }
            this.updateFormData({
                key: "dealershipFile2",
                value: this.form.dealershipFile2,
            });
        },
        handleDealershipFile2Removed() {
            if (this.form.dealershipFile2PreviewUrl) {
                URL.revokeObjectURL(this.form.dealershipFile2PreviewUrl);
            }
            this.form.dealershipFile2 = null;
            this.form.dealershipFile2PreviewUrl = null;
            this.updateFormData({
                key: "dealershipFile2",
                value: null,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.preview-wrapper {
    display: flex;
    justify-content: center;
}

.preview {
    margin: 0 auto;
}

.configuration {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.disclaimer {
    font-size: px2rem(12);
    font-style: italic;
}
.note {
    display: flex;
    flex-direction: column;
    font-size: px2rem(14);
    font-style: normal;
    line-height: 20px;

    .note-title {
        font-weight: 600;
        margin-bottom: 4px;
    }
}

@media (max-width: 768px) {
    .card-group {
        flex-direction: column !important;
    }

    .preview {
        margin-bottom: 16px;
        width: 100%;
    }
}
</style>
